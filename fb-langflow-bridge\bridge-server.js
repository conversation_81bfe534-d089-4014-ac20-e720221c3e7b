// bridge-server.js
require('dotenv').config();
const express = require('express');
const axios = require('axios');
const app = express();
const port = process.env.PORT || 3000;

// Configuration từ environment variables
const VERIFY_TOKEN = process.env.VERIFY_TOKEN || '12345';
const PAGE_ACCESS_TOKEN = process.env.FB_TOKEN; // Từ file .env của bạn
const LANGFLOW_API_URL = 'http://127.0.0.1:7860/api/v1/run/8096dc20-a7e5-4c25-9f07-acf4a901e857'; // URL từ Langflow của bạn

// Middleware
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`\n[${new Date().toISOString()}] ${req.method} ${req.url}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// Home route để kiểm tra server
app.get('/', (req, res) => {
  res.send(`
    <h1>Facebook-Langflow Bridge Server</h1>
    <p>✅ Server is running!</p>
    <ul>
      <li>Webhook endpoint: /webhook</li>
      <li>Verify token: ${VERIFY_TOKEN}</li>
      <li>Langflow connected: ${LANGFLOW_API_URL}</li>
      <li>Facebook token: ${PAGE_ACCESS_TOKEN ? '✅ Configured' : '❌ Missing'}</li>
    </ul>
  `);
});

// Webhook verification cho Facebook
app.get('/webhook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  console.log('🔍 Webhook Verification Request:', { mode, token, challenge });

  if (mode === 'subscribe' && token === VERIFY_TOKEN) {
    console.log('✅ Webhook verified successfully!');
    res.status(200).send(challenge);
  } else {
    console.error('❌ Verification failed');
    res.sendStatus(403);
  }
});

// Nhận messages từ Facebook
app.post('/webhook', async (req, res) => {
  const body = req.body;

  // Kiểm tra xem có phải message từ Facebook không
  if (body.object === 'page') {
    // Xử lý từng message entry
    for (const entry of body.entry) {
      for (const event of entry.messaging) {
        // Kiểm tra có phải là tin nhắn text không
        if (event.message && event.message.text && !event.message.is_echo) {
          // Xử lý message async (không block response)
          handleMessage(event).catch(console.error);
        }
      }
    }
    
    // Trả về 200 OK ngay lập tức cho Facebook
    res.status(200).send('EVENT_RECEIVED');
  } else {
    res.sendStatus(404);
  }
});

// Xử lý message và gọi Langflow
async function handleMessage(event) {
  const senderId = event.sender.id;
  const messageText = event.message.text;
  const messageId = event.message.mid;

  console.log(`\n💬 New message from ${senderId}:`);
  console.log(`   Message: "${messageText}"`);
  console.log(`   Message ID: ${messageId}`);

  try {
    // Hiển thị "typing" indicator
    await sendTypingIndicator(senderId, true);

    // Gọi Langflow API
    console.log('🤖 Calling Langflow...');
    const langflowResponse = await callLangflow(messageText, senderId);
    
    // Tắt typing indicator
    await sendTypingIndicator(senderId, false);

    // Gửi response về Facebook
    await sendTextMessage(senderId, langflowResponse);
    
  } catch (error) {
    console.error('❌ Error processing message:', error);
    await sendTypingIndicator(senderId, false);
    await sendTextMessage(senderId, 'Xin lỗi, tôi gặp lỗi khi xử lý tin nhắn của bạn. Vui lòng thử lại sau.');
  }
}

// Gọi Langflow API
async function callLangflow(message, userId) {
  const payload = {
    "output_type": "chat",
    "input_type": "chat",
    "input_value": message,
    "session_id": userId // Dùng Facebook user ID làm session
  };

  const headers = {
    "Content-Type": "application/json"
  };

  try {
    console.log('📤 Sending to Langflow:', payload);
    
    const response = await axios.post(LANGFLOW_API_URL, payload, { headers });
    
    console.log('📥 Langflow raw response:', JSON.stringify(response.data, null, 2));

    // Parse response từ Langflow
    // Cấu trúc response có thể khác nhau tùy flow
    if (response.data) {
      // Nếu response là string
      if (typeof response.data === 'string') {
        return response.data;
      }
      
      // Nếu có outputs array
      if (response.data.outputs && Array.isArray(response.data.outputs)) {
        // Thử nhiều cách parse khác nhau
        const output = response.data.outputs[0];
        
        // Cách 1: Direct message
        if (output?.message) {
          return output.message;
        }
        
        // Cách 2: Nested outputs
        if (output?.outputs?.[0]?.results?.message?.text) {
          return output.outputs[0].results.message.text;
        }
        
        // Cách 3: Output value
        if (output?.output_value) {
          return output.output_value;
        }
      }
      
      // Nếu có result trực tiếp
      if (response.data.result) {
        return response.data.result;
      }
    }

    return "Không nhận được phản hồi từ Langflow";
    
  } catch (error) {
    console.error('❌ Langflow API error:', error.response?.data || error.message);
    throw error;
  }
}

// Gửi tin nhắn về Facebook
async function sendTextMessage(recipientId, messageText) {
  const messageData = {
    recipient: { id: recipientId },
    message: { text: messageText }
  };

  try {
    console.log(`📨 Sending to Facebook user ${recipientId}: "${messageText}"`);
    
    const response = await axios.post(
      `https://graph.facebook.com/v18.0/me/messages?access_token=${PAGE_ACCESS_TOKEN}`,
      messageData
    );
    
    console.log('✅ Message sent successfully:', response.data);
    
  } catch (error) {
    console.error('❌ Failed to send message to Facebook:', error.response?.data || error.message);
    throw error;
  }
}

// Hiển thị typing indicator
async function sendTypingIndicator(recipientId, isTyping) {
  const data = {
    recipient: { id: recipientId },
    sender_action: isTyping ? "typing_on" : "typing_off"
  };

  try {
    await axios.post(
      `https://graph.facebook.com/v18.0/me/messages?access_token=${PAGE_ACCESS_TOKEN}`,
      data
    );
  } catch (error) {
    console.error('Failed to send typing indicator:', error.message);
  }
}

// Test endpoint để kiểm tra Langflow
app.post('/test-langflow', async (req, res) => {
  const testMessage = req.body.message || "Xin chào";
  
  try {
    console.log('🧪 Testing Langflow with message:', testMessage);
    const response = await callLangflow(testMessage, "test-user");
    res.json({ 
      success: true, 
      input: testMessage,
      response: response 
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: error.message,
      details: error.response?.data 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    config: {
      facebook_token: PAGE_ACCESS_TOKEN ? 'configured' : 'missing',
      langflow_url: LANGFLOW_API_URL,
      verify_token: VERIFY_TOKEN
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(port, () => {
  console.log(`
🚀 Facebook-Langflow Bridge Server Started!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📍 Server URL: http://localhost:${port}
🔑 Verify Token: ${VERIFY_TOKEN}
🤖 Langflow API: ${LANGFLOW_API_URL}
📱 Facebook Token: ${PAGE_ACCESS_TOKEN ? '✅ Loaded from .env' : '❌ NOT FOUND'}

📋 Available endpoints:
   GET  /              - Server info
   GET  /webhook       - Facebook verification
   POST /webhook       - Receive messages
   POST /test-langflow - Test Langflow connection
   GET  /health        - Health check

⚡ Ready to bridge messages!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
  `);
  
  if (!PAGE_ACCESS_TOKEN) {
    console.error('⚠️  WARNING: FB_TOKEN not found in .env file!');
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Server shutting down...');
  process.exit(0);
});