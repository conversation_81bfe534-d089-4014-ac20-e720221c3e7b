/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2563eb;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav a:hover {
    color: #2563eb;
}

/* Main Content */
.main {
    margin-top: 80px;
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero-content h2 {
    font-size: 3rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: 1.25rem;
    color: #64748b;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Features */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.feature .icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.feature h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.feature p {
    color: #64748b;
    font-size: 0.95rem;
}

/* About Section */
.about {
    padding: 4rem 0;
    background: #fff;
}

.about h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 3rem;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.about-item {
    text-align: center;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.about-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.about-item p {
    color: #64748b;
    line-height: 1.7;
}

/* Contact Section */
.contact {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.contact h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 3rem;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.contact-item {
    text-align: center;
    padding: 1.5rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    min-width: 250px;
}

.contact-item strong {
    color: #2563eb;
    font-weight: 600;
}

/* Footer */
.footer {
    background: #1e293b;
    color: #94a3b8;
    padding: 2rem 0;
}

.footer-content {
    text-align: center;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #fff;
}

.footer p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        gap: 1rem;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
    
    .about-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-info {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Policy Pages Styles */
.policy-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.policy-content h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
    text-align: center;
}

.last-updated {
    text-align: center;
    color: #64748b;
    font-style: italic;
    margin-bottom: 3rem;
}

.policy-section {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #2563eb;
}

.policy-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.policy-section h3 {
    font-size: 1.25rem;
    font-weight: 500;
    color: #374151;
    margin: 1.5rem 0 0.75rem 0;
}

.policy-section p {
    color: #4b5563;
    line-height: 1.7;
    margin-bottom: 1rem;
}

.policy-section ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.policy-section li {
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.policy-section a {
    color: #2563eb;
    text-decoration: none;
}

.policy-section a:hover {
    text-decoration: underline;
}

.contact-info {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-top: 1rem;
}

.contact-info p {
    margin-bottom: 0.5rem;
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .main {
        margin-top: 120px;
    }

    .hero {
        padding: 2rem 0;
    }

    .about, .contact {
        padding: 2rem 0;
    }

    .policy-content {
        padding: 1rem 0;
    }

    .policy-content h1 {
        font-size: 2rem;
    }

    .policy-section {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
}
